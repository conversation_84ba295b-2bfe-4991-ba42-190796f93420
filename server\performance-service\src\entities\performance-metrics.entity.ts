/**
 * 性能指标实体
 */

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('performance_metrics')
@Index(['nodeId', 'timestamp'])
@Index(['timestamp'])
export class PerformanceMetricsEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'node_id', length: 100 })
  @Index()
  nodeId: string;

  @Column({ type: 'bigint' })
  @Index()
  timestamp: number;

  // CPU指标
  @Column({ name: 'cpu_usage', type: 'decimal', precision: 5, scale: 2 })
  cpuUsage: number;

  @Column({ name: 'cpu_load_average', type: 'json' })
  cpuLoadAverage: number[];

  @Column({ name: 'cpu_cores', type: 'int' })
  cpuCores: number;

  // 内存指标
  @Column({ name: 'memory_used', type: 'bigint' })
  memoryUsed: number;

  @Column({ name: 'memory_total', type: 'bigint' })
  memoryTotal: number;

  @Column({ name: 'memory_usage', type: 'decimal', precision: 5, scale: 2 })
  memoryUsage: number;

  @Column({ name: 'heap_used', type: 'bigint' })
  heapUsed: number;

  @Column({ name: 'heap_total', type: 'bigint' })
  heapTotal: number;

  // 网络指标
  @Column({ name: 'network_bytes_in', type: 'bigint', default: 0 })
  networkBytesIn: number;

  @Column({ name: 'network_bytes_out', type: 'bigint', default: 0 })
  networkBytesOut: number;

  @Column({ name: 'network_connections_active', type: 'int', default: 0 })
  networkConnectionsActive: number;

  // 应用指标
  @Column({ name: 'requests_per_second', type: 'decimal', precision: 10, scale: 2, default: 0 })
  requestsPerSecond: number;

  @Column({ name: 'average_response_time', type: 'decimal', precision: 10, scale: 2, default: 0 })
  averageResponseTime: number;

  @Column({ name: 'error_rate', type: 'decimal', precision: 5, scale: 2, default: 0 })
  errorRate: number;

  @Column({ name: 'active_connections', type: 'int', default: 0 })
  activeConnections: number;

  // 优化指标
  @Column({ name: 'cache_hit_rate', type: 'decimal', precision: 5, scale: 2, default: 0 })
  cacheHitRate: number;

  @Column({ name: 'pool_utilization', type: 'decimal', precision: 5, scale: 2, default: 0 })
  poolUtilization: number;

  @Column({ name: 'parallel_efficiency', type: 'decimal', precision: 5, scale: 2, default: 0 })
  parallelEfficiency: number;

  @Column({ name: 'gc_pressure', type: 'decimal', precision: 5, scale: 2, default: 0 })
  gcPressure: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
