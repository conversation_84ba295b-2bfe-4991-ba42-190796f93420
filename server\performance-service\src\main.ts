/**
 * 性能优化服务主入口文件
 */

import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as compression from 'compression';
import * as helmet from 'helmet';
import rateLimit from 'express-rate-limit';

import { AppModule } from './app.module';
import { PerformanceInterceptor } from './interceptors/performance.interceptor';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 全局前缀
    app.setGlobalPrefix('api/v1');

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // 全局性能监控拦截器
    app.useGlobalInterceptors(new PerformanceInterceptor());

    // 安全中间件
    app.use(helmet());

    // 压缩中间件
    if (configService.get<boolean>('ENABLE_COMPRESSION', true)) {
      app.use(compression({
        threshold: configService.get<number>('COMPRESSION_THRESHOLD', 1024),
      }));
    }

    // 限流中间件
    app.use(
      rateLimit({
        windowMs: configService.get<number>('RATE_LIMIT_WINDOW_MS', 15 * 60 * 1000),
        max: configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
        message: '请求过于频繁，请稍后再试',
      }),
    );

    // CORS配置
    app.enableCors({
      origin: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    });

    // Swagger文档配置
    if (configService.get<boolean>('SWAGGER_ENABLED', true)) {
      const config = new DocumentBuilder()
        .setTitle(configService.get<string>('SWAGGER_TITLE', '性能优化服务API'))
        .setDescription(configService.get<string>('SWAGGER_DESCRIPTION', '提供分布式性能监控、自动调优和负载均衡优化功能'))
        .setVersion(configService.get<string>('SWAGGER_VERSION', '1.0.0'))
        .addBearerAuth()
        .addTag('性能优化', '性能监控和优化相关接口')
        .addTag('健康检查', '服务健康状态检查')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup(
        configService.get<string>('SWAGGER_PATH', 'api/docs'),
        app,
        document,
        {
          swaggerOptions: {
            persistAuthorization: true,
          },
        },
      );
    }

    // 微服务配置
    const microservicePort = configService.get<number>('MICROSERVICE_PORT', 3061);
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: '0.0.0.0',
        port: microservicePort,
      },
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log(`微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3060);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 性能优化服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📖 API文档地址: http://${host}:${port}/${configService.get<string>('SWAGGER_PATH', 'api/docs')}`);
    logger.log(`🔍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('应用启动失败:', error);
    process.exit(1);
  }
}

bootstrap();
