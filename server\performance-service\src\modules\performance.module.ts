/**
 * 性能优化模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';

import { PerformanceController } from '../controllers/performance.controller';
import { PerformanceOptimizationService } from '../services/performance-optimization.service';
import { PerformanceMetricsService } from '../services/performance-metrics.service';
import { LoadBalancingService } from '../services/load-balancing.service';
import { CacheOptimizationService } from '../services/cache-optimization.service';

import { PerformanceMetricsEntity } from '../entities/performance-metrics.entity';
import { OptimizationConfigEntity } from '../entities/optimization-config.entity';
import { NodeStatusEntity } from '../entities/node-status.entity';

import { RedisService } from '../services/redis.service';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule,
    TypeOrmModule.forFeature([
      PerformanceMetricsEntity,
      OptimizationConfigEntity,
      NodeStatusEntity,
    ]),
  ],
  controllers: [
    PerformanceController,
  ],
  providers: [
    PerformanceOptimizationService,
    PerformanceMetricsService,
    LoadBalancingService,
    CacheOptimizationService,
    RedisService,
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('redis.host'),
        port: configService.get<number>('redis.port'),
        password: configService.get<string>('redis.password'),
        db: configService.get<number>('redis.db'),
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      }),
      inject: [ConfigService],
    },
  ],
  exports: [
    PerformanceOptimizationService,
    PerformanceMetricsService,
    LoadBalancingService,
    CacheOptimizationService,
    RedisService,
  ],
})
export class PerformanceModule {}
