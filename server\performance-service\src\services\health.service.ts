/**
 * 健康检查服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  async check() {
    const startTime = Date.now();
    
    try {
      // 基础健康检查
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.configService.get<string>('app.serviceVersion'),
        environment: this.configService.get<string>('app.nodeEnv'),
        responseTime: Date.now() - startTime,
      };

      return health;
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      throw error;
    }
  }

  async detailedCheck() {
    const startTime = Date.now();
    
    try {
      // 检查Redis连接
      const redisHealth = await this.checkRedis();
      
      // 检查内存使用
      const memoryUsage = process.memoryUsage();
      const memoryHealth = this.checkMemory(memoryUsage);
      
      // 检查CPU使用
      const cpuUsage = process.cpuUsage();
      const cpuHealth = this.checkCpu(cpuUsage);
      
      const health = {
        status: this.determineOverallStatus([redisHealth, memoryHealth, cpuHealth]),
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: this.configService.get<string>('app.serviceVersion'),
        environment: this.configService.get<string>('app.nodeEnv'),
        responseTime: Date.now() - startTime,
        checks: {
          redis: redisHealth,
          memory: memoryHealth,
          cpu: cpuHealth,
        },
        system: {
          memory: memoryUsage,
          cpu: cpuUsage,
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
        },
      };

      return health;
    } catch (error) {
      this.logger.error('详细健康检查失败:', error);
      throw error;
    }
  }

  async readinessCheck(): Promise<boolean> {
    try {
      // 检查关键依赖是否就绪
      await this.checkRedis();
      
      // 检查服务是否已完全启动
      const uptime = process.uptime();
      if (uptime < 10) { // 服务启动不足10秒
        return false;
      }
      
      return true;
    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return false;
    }
  }

  private async checkRedis() {
    try {
      const startTime = Date.now();
      await this.redisService.ping();
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
        message: 'Redis连接正常',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        message: 'Redis连接失败',
      };
    }
  }

  private checkMemory(memoryUsage: NodeJS.MemoryUsage) {
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
    const usagePercent = (heapUsedMB / heapTotalMB) * 100;
    
    let status = 'healthy';
    let message = '内存使用正常';
    
    if (usagePercent > 90) {
      status = 'critical';
      message = '内存使用率过高';
    } else if (usagePercent > 80) {
      status = 'warning';
      message = '内存使用率较高';
    }
    
    return {
      status,
      message,
      usage: {
        heapUsedMB: Math.round(heapUsedMB),
        heapTotalMB: Math.round(heapTotalMB),
        usagePercent: Math.round(usagePercent),
      },
    };
  }

  private checkCpu(cpuUsage: NodeJS.CpuUsage) {
    // 简单的CPU检查，实际应用中可能需要更复杂的逻辑
    const userTime = cpuUsage.user / 1000; // 转换为毫秒
    const systemTime = cpuUsage.system / 1000;
    
    return {
      status: 'healthy',
      message: 'CPU使用正常',
      usage: {
        user: userTime,
        system: systemTime,
        total: userTime + systemTime,
      },
    };
  }

  private determineOverallStatus(checks: any[]): string {
    const statuses = checks.map(check => check.status);
    
    if (statuses.includes('critical')) {
      return 'critical';
    }
    
    if (statuses.includes('unhealthy')) {
      return 'unhealthy';
    }
    
    if (statuses.includes('warning')) {
      return 'warning';
    }
    
    return 'healthy';
  }
}
