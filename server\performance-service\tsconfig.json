{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@controllers/*": ["src/controllers/*"], "@services/*": ["src/services/*"], "@dto/*": ["src/dto/*"], "@guards/*": ["src/guards/*"], "@middleware/*": ["src/middleware/*"], "@interceptors/*": ["src/interceptors/*"], "@config/*": ["src/config/*"], "@constants/*": ["src/constants/*"], "@utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}