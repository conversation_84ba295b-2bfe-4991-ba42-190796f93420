/**
 * 节点状态实体
 */

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('node_status')
export class NodeStatusEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'node_id', length: 100, unique: true })
  @Index()
  nodeId: string;

  @Column({ name: 'status', length: 20, default: 'healthy' })
  status: string; // healthy, warning, critical, offline

  @Column({ name: 'last_seen', type: 'bigint' })
  lastSeen: number;

  @Column({ name: 'uptime', type: 'bigint', default: 0 })
  uptime: number;

  @Column({ name: 'version', length: 50, nullable: true })
  version: string;

  @Column({ name: 'environment', length: 50, nullable: true })
  environment: string;

  @Column({ name: 'hostname', length: 255, nullable: true })
  hostname: string;

  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string;

  @Column({ name: 'port', type: 'int', nullable: true })
  port: number;

  @Column({ name: 'cpu_cores', type: 'int', default: 1 })
  cpuCores: number;

  @Column({ name: 'total_memory', type: 'bigint', default: 0 })
  totalMemory: number;

  @Column({ name: 'platform', length: 50, nullable: true })
  platform: string;

  @Column({ name: 'architecture', length: 50, nullable: true })
  architecture: string;

  @Column({ name: 'node_version', length: 50, nullable: true })
  nodeVersion: string;

  @Column({ name: 'current_load', type: 'decimal', precision: 5, scale: 2, default: 0 })
  currentLoad: number;

  @Column({ name: 'request_count', type: 'bigint', default: 0 })
  requestCount: number;

  @Column({ name: 'error_count', type: 'bigint', default: 0 })
  errorCount: number;

  @Column({ name: 'last_error', type: 'text', nullable: true })
  lastError: string;

  @Column({ name: 'last_error_time', type: 'bigint', nullable: true })
  lastErrorTime: number;

  @Column({ name: 'health_check_interval', type: 'int', default: 30000 })
  healthCheckInterval: number;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
