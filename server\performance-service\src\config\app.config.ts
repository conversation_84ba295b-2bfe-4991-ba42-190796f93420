/**
 * 应用配置
 */

import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  // 基础配置
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3060,
  host: process.env.HOST || '0.0.0.0',
  
  // 服务信息
  serviceName: process.env.SERVICE_NAME || 'performance-service',
  serviceVersion: process.env.SERVICE_VERSION || '1.0.0',
  
  // JWT配置
  jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-here',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
  
  // 性能监控配置
  enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
  performanceMetricsInterval: parseInt(process.env.PERFORMANCE_METRICS_INTERVAL, 10) || 5000,
  enableAutoTuning: process.env.ENABLE_AUTO_TUNING === 'true',
  enableLoadBalancing: process.env.ENABLE_LOAD_BALANCING === 'true',
  
  // 缓存配置
  enableCaching: process.env.ENABLE_CACHING !== 'false',
  cacheTtl: parseInt(process.env.CACHE_TTL, 10) || 3600,
  cacheSize: parseInt(process.env.CACHE_SIZE, 10) || 1000,
  
  // 压缩配置
  enableCompression: process.env.ENABLE_COMPRESSION !== 'false',
  compressionThreshold: parseInt(process.env.COMPRESSION_THRESHOLD, 10) || 1024,
  
  // 限流配置
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000,
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
  
  // 日志配置
  logLevel: process.env.LOG_LEVEL || 'info',
  logFormat: process.env.LOG_FORMAT || 'json',
  
  // Swagger配置
  swaggerEnabled: process.env.SWAGGER_ENABLED !== 'false',
  swaggerPath: process.env.SWAGGER_PATH || 'api/docs',
  swaggerTitle: process.env.SWAGGER_TITLE || '性能优化服务API',
  swaggerDescription: process.env.SWAGGER_DESCRIPTION || '提供分布式性能监控、自动调优和负载均衡优化功能',
  swaggerVersion: process.env.SWAGGER_VERSION || '1.0.0',
  
  // 微服务配置
  microservicePort: parseInt(process.env.MICROSERVICE_PORT, 10) || 3061,
  microserviceTransport: process.env.MICROSERVICE_TRANSPORT || 'TCP',
  
  // 健康检查配置
  healthCheckEnabled: process.env.HEALTH_CHECK_ENABLED !== 'false',
  healthCheckTimeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT, 10) || 5000,
  
  // 监控告警配置
  alertCpuThreshold: parseInt(process.env.ALERT_CPU_THRESHOLD, 10) || 80,
  alertMemoryThreshold: parseInt(process.env.ALERT_MEMORY_THRESHOLD, 10) || 85,
  alertResponseTimeThreshold: parseInt(process.env.ALERT_RESPONSE_TIME_THRESHOLD, 10) || 2000,
  alertErrorRateThreshold: parseInt(process.env.ALERT_ERROR_RATE_THRESHOLD, 10) || 5,
  
  // 负载均衡配置
  loadBalanceStrategy: process.env.LOAD_BALANCE_STRATEGY || 'cpu_based',
  rebalanceInterval: parseInt(process.env.REBALANCE_INTERVAL, 10) || 30000,
  
  // 优化配置
  maxMemoryUsage: parseInt(process.env.MAX_MEMORY_USAGE, 10) || 85,
  maxCpuUsage: parseInt(process.env.MAX_CPU_USAGE, 10) || 80,
  gcThreshold: parseInt(process.env.GC_THRESHOLD, 10) || 70,
  parallelThreshold: parseInt(process.env.PARALLEL_THRESHOLD, 10) || 4,
  poolSize: parseInt(process.env.POOL_SIZE, 10) || 10,
}));
