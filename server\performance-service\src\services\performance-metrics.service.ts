/**
 * 性能指标服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';

import { PerformanceMetricsEntity } from '../entities/performance-metrics.entity';
import { RedisService } from './redis.service';
import { PerformanceMetrics } from './performance-optimization.service';
import { CACHE_KEYS, PERFORMANCE_EVENTS, DEFAULT_CONFIG } from '../constants/performance.constants';

@Injectable()
export class PerformanceMetricsService {
  private readonly logger = new Logger(PerformanceMetricsService.name);

  constructor(
    @InjectRepository(PerformanceMetricsEntity)
    private readonly metricsRepository: Repository<PerformanceMetricsEntity>,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 保存性能指标
   */
  async saveMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      // 保存到数据库
      const entity = this.metricsRepository.create({
        nodeId: metrics.nodeId,
        timestamp: metrics.timestamp,
        cpuUsage: metrics.cpu.usage,
        cpuLoadAverage: metrics.cpu.loadAverage,
        cpuCores: metrics.cpu.cores,
        memoryUsed: metrics.memory.used,
        memoryTotal: metrics.memory.total,
        memoryUsage: metrics.memory.usage,
        heapUsed: metrics.memory.heapUsed,
        heapTotal: metrics.memory.heapTotal,
        networkBytesIn: metrics.network.bytesIn,
        networkBytesOut: metrics.network.bytesOut,
        networkConnectionsActive: metrics.network.connectionsActive,
        requestsPerSecond: metrics.application.requestsPerSecond,
        averageResponseTime: metrics.application.averageResponseTime,
        errorRate: metrics.application.errorRate,
        activeConnections: metrics.application.activeConnections,
        cacheHitRate: metrics.optimization.cacheHitRate,
        poolUtilization: metrics.optimization.poolUtilization,
        parallelEfficiency: metrics.optimization.parallelEfficiency,
        gcPressure: metrics.optimization.gcPressure,
      });

      await this.metricsRepository.save(entity);

      // 缓存最新指标
      const cacheKey = `${CACHE_KEYS.PERFORMANCE_METRICS}:${metrics.nodeId}:latest`;
      await this.redisService.set(cacheKey, metrics, DEFAULT_CONFIG.CACHE_TTL);

      // 发送事件
      this.eventEmitter.emit(PERFORMANCE_EVENTS.METRICS_COLLECTED, metrics);

      this.logger.debug(`性能指标已保存: ${metrics.nodeId}`);
    } catch (error) {
      this.logger.error('保存性能指标失败:', error);
      throw error;
    }
  }

  /**
   * 获取性能指标
   */
  async getMetrics(
    nodeId: string,
    startTime?: number,
    endTime?: number,
    limit: number = 100,
    offset: number = 0,
  ): Promise<PerformanceMetricsEntity[]> {
    try {
      const queryBuilder = this.metricsRepository
        .createQueryBuilder('metrics')
        .where('metrics.nodeId = :nodeId', { nodeId })
        .orderBy('metrics.timestamp', 'DESC')
        .limit(limit)
        .offset(offset);

      if (startTime && endTime) {
        queryBuilder.andWhere('metrics.timestamp BETWEEN :startTime AND :endTime', {
          startTime,
          endTime,
        });
      } else if (startTime) {
        queryBuilder.andWhere('metrics.timestamp >= :startTime', { startTime });
      } else if (endTime) {
        queryBuilder.andWhere('metrics.timestamp <= :endTime', { endTime });
      }

      return await queryBuilder.getMany();
    } catch (error) {
      this.logger.error('获取性能指标失败:', error);
      throw error;
    }
  }

  /**
   * 获取最新性能指标
   */
  async getLatestMetrics(nodeId: string): Promise<PerformanceMetrics | null> {
    try {
      // 先从缓存获取
      const cacheKey = `${CACHE_KEYS.PERFORMANCE_METRICS}:${nodeId}:latest`;
      const cached = await this.redisService.get<PerformanceMetrics>(cacheKey);
      
      if (cached) {
        return cached;
      }

      // 从数据库获取
      const entity = await this.metricsRepository.findOne({
        where: { nodeId },
        order: { timestamp: 'DESC' },
      });

      if (!entity) {
        return null;
      }

      const metrics = this.entityToMetrics(entity);
      
      // 缓存结果
      await this.redisService.set(cacheKey, metrics, DEFAULT_CONFIG.CACHE_TTL);
      
      return metrics;
    } catch (error) {
      this.logger.error('获取最新性能指标失败:', error);
      throw error;
    }
  }

  /**
   * 获取性能统计
   */
  async getMetricsStatistics(
    nodeId: string,
    period: string = '1h',
  ): Promise<any> {
    try {
      const now = Date.now();
      let startTime: number;

      switch (period) {
        case '1h':
          startTime = now - 60 * 60 * 1000;
          break;
        case '6h':
          startTime = now - 6 * 60 * 60 * 1000;
          break;
        case '24h':
          startTime = now - 24 * 60 * 60 * 1000;
          break;
        case '7d':
          startTime = now - 7 * 24 * 60 * 60 * 1000;
          break;
        default:
          startTime = now - 60 * 60 * 1000;
      }

      const metrics = await this.metricsRepository.find({
        where: {
          nodeId,
          timestamp: MoreThan(startTime),
        },
        order: { timestamp: 'ASC' },
      });

      if (metrics.length === 0) {
        return null;
      }

      return this.calculateStatistics(metrics);
    } catch (error) {
      this.logger.error('获取性能统计失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期指标数据
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredMetrics(): Promise<void> {
    try {
      const retentionDays = 30; // 保留30天数据
      const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1000;

      const result = await this.metricsRepository.delete({
        timestamp: Between(0, cutoffTime),
      });

      this.logger.log(`清理过期性能指标: ${result.affected} 条记录`);
    } catch (error) {
      this.logger.error('清理过期指标失败:', error);
    }
  }

  /**
   * 实体转换为指标对象
   */
  private entityToMetrics(entity: PerformanceMetricsEntity): PerformanceMetrics {
    return {
      nodeId: entity.nodeId,
      timestamp: entity.timestamp,
      cpu: {
        usage: Number(entity.cpuUsage),
        loadAverage: entity.cpuLoadAverage,
        cores: entity.cpuCores,
      },
      memory: {
        used: entity.memoryUsed,
        total: entity.memoryTotal,
        usage: Number(entity.memoryUsage),
        heapUsed: entity.heapUsed,
        heapTotal: entity.heapTotal,
      },
      network: {
        bytesIn: entity.networkBytesIn,
        bytesOut: entity.networkBytesOut,
        connectionsActive: entity.networkConnectionsActive,
      },
      application: {
        requestsPerSecond: Number(entity.requestsPerSecond),
        averageResponseTime: Number(entity.averageResponseTime),
        errorRate: Number(entity.errorRate),
        activeConnections: entity.activeConnections,
      },
      optimization: {
        cacheHitRate: Number(entity.cacheHitRate),
        poolUtilization: Number(entity.poolUtilization),
        parallelEfficiency: Number(entity.parallelEfficiency),
        gcPressure: Number(entity.gcPressure),
      },
    };
  }

  /**
   * 计算统计数据
   */
  private calculateStatistics(metrics: PerformanceMetricsEntity[]): any {
    const cpuUsages = metrics.map(m => Number(m.cpuUsage));
    const memoryUsages = metrics.map(m => Number(m.memoryUsage));
    const responseTimes = metrics.map(m => Number(m.averageResponseTime));
    const errorRates = metrics.map(m => Number(m.errorRate));

    return {
      cpu: {
        average: this.calculateAverage(cpuUsages),
        min: Math.min(...cpuUsages),
        max: Math.max(...cpuUsages),
        current: cpuUsages[cpuUsages.length - 1],
      },
      memory: {
        average: this.calculateAverage(memoryUsages),
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages),
        current: memoryUsages[memoryUsages.length - 1],
      },
      responseTime: {
        average: this.calculateAverage(responseTimes),
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes),
        current: responseTimes[responseTimes.length - 1],
      },
      errorRate: {
        average: this.calculateAverage(errorRates),
        min: Math.min(...errorRates),
        max: Math.max(...errorRates),
        current: errorRates[errorRates.length - 1],
      },
      dataPoints: metrics.length,
      timeRange: {
        start: metrics[0].timestamp,
        end: metrics[metrics.length - 1].timestamp,
      },
    };
  }

  /**
   * 计算平均值
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }
}
