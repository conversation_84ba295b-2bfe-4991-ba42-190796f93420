/**
 * 优化配置DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsNumber, IsPositive, Min, Max, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class OptimizationConfigDto {
  @ApiProperty({
    description: '是否启用自动调优',
    example: true,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableAutoTuning?: boolean;

  @ApiProperty({
    description: '是否启用负载均衡',
    example: true,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableLoadBalancing?: boolean;

  @ApiProperty({
    description: '是否启用缓存',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableCaching?: boolean;

  @ApiProperty({
    description: '是否启用压缩',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  enableCompression?: boolean;

  @ApiProperty({
    description: '最大内存使用率(%)',
    example: 85,
    minimum: 50,
    maximum: 95,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(50)
  @Max(95)
  maxMemoryUsage?: number;

  @ApiProperty({
    description: '最大CPU使用率(%)',
    example: 80,
    minimum: 50,
    maximum: 95,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(50)
  @Max(95)
  maxCpuUsage?: number;

  @ApiProperty({
    description: '缓存大小(MB)',
    example: 1000,
    minimum: 100,
    maximum: 10000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(100)
  @Max(10000)
  cacheSize?: number;

  @ApiProperty({
    description: '连接池大小',
    example: 10,
    minimum: 5,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(5)
  @Max(100)
  poolSize?: number;

  @ApiProperty({
    description: '并行处理阈值',
    example: 4,
    minimum: 1,
    maximum: 32,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(32)
  parallelThreshold?: number;

  @ApiProperty({
    description: 'GC压力阈值(%)',
    example: 70,
    minimum: 50,
    maximum: 90,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(50)
  @Max(90)
  gcThreshold?: number;

  @ApiProperty({
    description: '负载均衡策略',
    example: 'cpu_based',
    enum: ['round_robin', 'least_connections', 'cpu_based', 'memory_based'],
    required: false,
  })
  @IsOptional()
  @IsString()
  loadBalanceStrategy?: 'round_robin' | 'least_connections' | 'cpu_based' | 'memory_based';

  @ApiProperty({
    description: '重新平衡间隔(毫秒)',
    example: 30000,
    minimum: 5000,
    maximum: 300000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(5000)
  @Max(300000)
  rebalanceInterval?: number;

  @ApiProperty({
    description: '缓存TTL(秒)',
    example: 3600,
    minimum: 60,
    maximum: 86400,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(60)
  @Max(86400)
  cacheTtl?: number;

  @ApiProperty({
    description: '压缩阈值(字节)',
    example: 1024,
    minimum: 512,
    maximum: 10240,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(512)
  @Max(10240)
  compressionThreshold?: number;
}
