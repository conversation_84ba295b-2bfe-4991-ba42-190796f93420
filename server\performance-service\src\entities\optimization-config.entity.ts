/**
 * 优化配置实体
 */

import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('optimization_configs')
export class OptimizationConfigEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'node_id', length: 100, unique: true })
  @Index()
  nodeId: string;

  @Column({ name: 'enable_auto_tuning', type: 'boolean', default: false })
  enableAutoTuning: boolean;

  @Column({ name: 'enable_load_balancing', type: 'boolean', default: false })
  enableLoadBalancing: boolean;

  @Column({ name: 'enable_caching', type: 'boolean', default: true })
  enableCaching: boolean;

  @Column({ name: 'enable_compression', type: 'boolean', default: true })
  enableCompression: boolean;

  @Column({ name: 'max_memory_usage', type: 'int', default: 85 })
  maxMemoryUsage: number;

  @Column({ name: 'max_cpu_usage', type: 'int', default: 80 })
  maxCpuUsage: number;

  @Column({ name: 'cache_size', type: 'int', default: 1000 })
  cacheSize: number;

  @Column({ name: 'pool_size', type: 'int', default: 10 })
  poolSize: number;

  @Column({ name: 'parallel_threshold', type: 'int', default: 4 })
  parallelThreshold: number;

  @Column({ name: 'gc_threshold', type: 'int', default: 70 })
  gcThreshold: number;

  @Column({ name: 'load_balance_strategy', length: 50, default: 'cpu_based' })
  loadBalanceStrategy: string;

  @Column({ name: 'rebalance_interval', type: 'int', default: 30000 })
  rebalanceInterval: number;

  @Column({ name: 'cache_ttl', type: 'int', default: 3600 })
  cacheTtl: number;

  @Column({ name: 'compression_threshold', type: 'int', default: 1024 })
  compressionThreshold: number;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
