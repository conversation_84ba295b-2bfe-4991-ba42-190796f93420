/**
 * 性能记录DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsBoolean, IsString, IsPositive, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class PerformanceRecordDto {
  @ApiProperty({
    description: '响应时间(毫秒)',
    example: 150,
    minimum: 0,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  responseTime: number;

  @ApiProperty({
    description: '是否为错误请求',
    example: false,
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isError?: boolean = false;

  @ApiProperty({
    description: '请求路径',
    example: '/api/v1/performance/metrics',
    required: false,
  })
  @IsOptional()
  @IsString()
  requestPath?: string;

  @ApiProperty({
    description: 'HTTP方法',
    example: 'GET',
    required: false,
  })
  @IsOptional()
  @IsString()
  httpMethod?: string;

  @ApiProperty({
    description: 'HTTP状态码',
    example: 200,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  statusCode?: number;

  @ApiProperty({
    description: '用户代理',
    example: 'Mozilla/5.0...',
    required: false,
  })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiProperty({
    description: '客户端IP',
    example: '*************',
    required: false,
  })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiProperty({
    description: '请求大小(字节)',
    example: 1024,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  requestSize?: number;

  @ApiProperty({
    description: '响应大小(字节)',
    example: 2048,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  responseSize?: number;

  @ApiProperty({
    description: '内存使用量(MB)',
    example: 512,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  memoryUsage?: number;

  @ApiProperty({
    description: 'CPU使用率(%)',
    example: 45.5,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  cpuUsage?: number;
}
