/**
 * 缓存优化服务
 */

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron } from '@nestjs/schedule';

import { RedisService } from './redis.service';
import { CACHE_KEYS } from '../constants/performance.constants';

interface CacheStats {
  totalKeys: number;
  totalMemory: number;
  hitRate: number;
  missRate: number;
  evictions: number;
  expiredKeys: number;
}

interface CacheOptimizationResult {
  action: string;
  keysAffected: number;
  memoryFreed: number;
  recommendation: string;
}

@Injectable()
export class CacheOptimizationService {
  private readonly logger = new Logger(CacheOptimizationService.name);
  private cacheHits = 0;
  private cacheMisses = 0;

  constructor(
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 获取缓存统计
   */
  async getCacheStats(): Promise<CacheStats> {
    try {
      const info = await this.redisService.info();
      const lines = info.split('\r\n');
      
      let totalKeys = 0;
      let totalMemory = 0;
      let evictions = 0;
      let expiredKeys = 0;

      for (const line of lines) {
        if (line.startsWith('used_memory:')) {
          totalMemory = parseInt(line.split(':')[1]);
        } else if (line.startsWith('evicted_keys:')) {
          evictions = parseInt(line.split(':')[1]);
        } else if (line.startsWith('expired_keys:')) {
          expiredKeys = parseInt(line.split(':')[1]);
        }
      }

      // 获取总键数
      const keys = await this.redisService.keys('*');
      totalKeys = keys.length;

      const totalRequests = this.cacheHits + this.cacheMisses;
      const hitRate = totalRequests > 0 ? (this.cacheHits / totalRequests) * 100 : 0;
      const missRate = totalRequests > 0 ? (this.cacheMisses / totalRequests) * 100 : 0;

      return {
        totalKeys,
        totalMemory,
        hitRate: Math.round(hitRate * 100) / 100,
        missRate: Math.round(missRate * 100) / 100,
        evictions,
        expiredKeys,
      };
    } catch (error) {
      this.logger.error('获取缓存统计失败:', error);
      throw error;
    }
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(): void {
    this.cacheHits++;
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss(): void {
    this.cacheMisses++;
  }

  /**
   * 优化缓存
   */
  async optimizeCache(): Promise<CacheOptimizationResult[]> {
    try {
      const results: CacheOptimizationResult[] = [];

      // 清理过期键
      const expiredResult = await this.cleanupExpiredKeys();
      if (expiredResult.keysAffected > 0) {
        results.push(expiredResult);
      }

      // 清理低频访问键
      const lowFrequencyResult = await this.cleanupLowFrequencyKeys();
      if (lowFrequencyResult.keysAffected > 0) {
        results.push(lowFrequencyResult);
      }

      // 压缩大型键
      const compressionResult = await this.compressLargeKeys();
      if (compressionResult.keysAffected > 0) {
        results.push(compressionResult);
      }

      this.logger.log(`缓存优化完成，执行了 ${results.length} 个优化操作`);
      return results;
    } catch (error) {
      this.logger.error('缓存优化失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期键
   */
  private async cleanupExpiredKeys(): Promise<CacheOptimizationResult> {
    try {
      const keys = await this.redisService.keys('*');
      let expiredCount = 0;
      let memoryFreed = 0;

      for (const key of keys) {
        const ttl = await this.redisService.ttl(key);
        if (ttl === -2) { // 键已过期
          // 估算内存使用（简化计算）
          memoryFreed += key.length + 100; // 假设每个键平均100字节
          await this.redisService.del(key);
          expiredCount++;
        }
      }

      return {
        action: '清理过期键',
        keysAffected: expiredCount,
        memoryFreed,
        recommendation: expiredCount > 0 ? '建议设置合适的TTL值' : '过期键清理正常',
      };
    } catch (error) {
      this.logger.error('清理过期键失败:', error);
      return {
        action: '清理过期键',
        keysAffected: 0,
        memoryFreed: 0,
        recommendation: '清理失败，请检查Redis连接',
      };
    }
  }

  /**
   * 清理低频访问键
   */
  private async cleanupLowFrequencyKeys(): Promise<CacheOptimizationResult> {
    try {
      // 这里可以实现基于访问频率的清理逻辑
      // 由于Redis没有内置的访问频率统计，这里只是示例
      
      const performanceKeys = await this.redisService.keys(`${CACHE_KEYS.PERFORMANCE_METRICS}:*`);
      let cleanedCount = 0;
      let memoryFreed = 0;
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      for (const key of performanceKeys) {
        const data = await this.redisService.get(key);
        if (data && typeof data === 'object' && 'timestamp' in data) {
          const age = Date.now() - (data as any).timestamp;
          if (age > maxAge) {
            memoryFreed += key.length + JSON.stringify(data).length;
            await this.redisService.del(key);
            cleanedCount++;
          }
        }
      }

      return {
        action: '清理低频访问键',
        keysAffected: cleanedCount,
        memoryFreed,
        recommendation: cleanedCount > 0 ? '建议优化缓存策略' : '缓存访问模式正常',
      };
    } catch (error) {
      this.logger.error('清理低频访问键失败:', error);
      return {
        action: '清理低频访问键',
        keysAffected: 0,
        memoryFreed: 0,
        recommendation: '清理失败，请检查缓存配置',
      };
    }
  }

  /**
   * 压缩大型键
   */
  private async compressLargeKeys(): Promise<CacheOptimizationResult> {
    try {
      // 这里可以实现大型键的压缩逻辑
      // 实际实现中可能需要使用压缩算法
      
      return {
        action: '压缩大型键',
        keysAffected: 0,
        memoryFreed: 0,
        recommendation: '当前没有需要压缩的大型键',
      };
    } catch (error) {
      this.logger.error('压缩大型键失败:', error);
      return {
        action: '压缩大型键',
        keysAffected: 0,
        memoryFreed: 0,
        recommendation: '压缩失败，请检查数据格式',
      };
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache(keys: string[]): Promise<void> {
    try {
      this.logger.log(`开始预热缓存，键数量: ${keys.length}`);
      
      for (const key of keys) {
        // 这里可以实现具体的预热逻辑
        // 例如从数据库加载数据到缓存
        await this.redisService.get(key);
      }
      
      this.logger.log('缓存预热完成');
    } catch (error) {
      this.logger.error('缓存预热失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存命中率
   */
  getCacheHitRate(): number {
    const totalRequests = this.cacheHits + this.cacheMisses;
    return totalRequests > 0 ? (this.cacheHits / totalRequests) * 100 : 0;
  }

  /**
   * 重置缓存统计
   */
  resetCacheStats(): void {
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.logger.log('缓存统计已重置');
  }

  /**
   * 定期缓存优化
   */
  @Cron('0 2 * * *') // 每天凌晨2点执行
  async scheduledCacheOptimization(): Promise<void> {
    try {
      this.logger.log('开始定期缓存优化');
      const results = await this.optimizeCache();
      
      const totalKeysAffected = results.reduce((sum, result) => sum + result.keysAffected, 0);
      const totalMemoryFreed = results.reduce((sum, result) => sum + result.memoryFreed, 0);
      
      this.logger.log(`定期缓存优化完成: 影响键数 ${totalKeysAffected}, 释放内存 ${totalMemoryFreed} 字节`);
    } catch (error) {
      this.logger.error('定期缓存优化失败:', error);
    }
  }

  /**
   * 监控缓存健康状态
   */
  async checkCacheHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const stats = await this.getCacheStats();
      const issues: string[] = [];
      const recommendations: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // 检查命中率
      if (stats.hitRate < 50) {
        status = 'warning';
        issues.push('缓存命中率过低');
        recommendations.push('优化缓存策略，增加缓存时间');
      }

      // 检查内存使用
      if (stats.totalMemory > 1024 * 1024 * 1024) { // 1GB
        status = 'warning';
        issues.push('缓存内存使用过高');
        recommendations.push('清理不必要的缓存数据');
      }

      // 检查驱逐率
      if (stats.evictions > 1000) {
        status = 'critical';
        issues.push('缓存驱逐过于频繁');
        recommendations.push('增加缓存容量或优化数据结构');
      }

      return { status, issues, recommendations };
    } catch (error) {
      this.logger.error('检查缓存健康状态失败:', error);
      return {
        status: 'critical',
        issues: ['无法获取缓存状态'],
        recommendations: ['检查Redis连接'],
      };
    }
  }
}
