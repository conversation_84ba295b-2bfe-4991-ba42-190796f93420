/**
 * 性能优化API控制器
 * 
 * 提供性能监控和优化的REST API接口
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  UseGuards,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth
} from '@nestjs/swagger';
import { 
  PerformanceOptimizationService,
  PerformanceMetrics,
  OptimizationConfig,
  OptimizationRecommendation
} from '../services/performance-optimization.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { PerformanceQueryDto } from '../dto/performance-query.dto';
import { OptimizationConfigDto } from '../dto/optimization-config.dto';



/**
 * 性能优化控制器
 */
@ApiTags('性能优化')
@Controller('api/performance')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PerformanceController {
  private readonly logger = new Logger(PerformanceController.name);

  constructor(
    private readonly performanceService: PerformanceOptimizationService
  ) {}

  /**
   * 获取性能指标
   */
  @Get('metrics')
  @ApiOperation({ summary: '获取性能指标' })
  @ApiQuery({ name: 'nodeId', required: false, description: '节点ID' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间戳' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间戳' })
  @ApiQuery({ name: 'limit', required: false, description: '限制数量' })
  @ApiQuery({ name: 'offset', required: false, description: '偏移量' })
  @ApiResponse({ status: 200, description: '成功获取性能指标' })
  async getPerformanceMetrics(@Query() query: PerformanceQueryDto) {
    try {
      const { nodeId, startTime, endTime, limit, offset } = query;
      
      if (!nodeId) {
        throw new HttpException('节点ID不能为空', HttpStatus.BAD_REQUEST);
      }
      
      let metrics = this.performanceService.getPerformanceMetrics(nodeId);
      
      // 时间范围过滤
      if (startTime || endTime) {
        metrics = metrics.filter(m => {
          if (startTime && m.timestamp < startTime) return false;
          if (endTime && m.timestamp > endTime) return false;
          return true;
        });
      }
      
      // 分页
      const total = metrics.length;
      const paginatedMetrics = metrics.slice(offset, offset + limit);
      
      return {
        success: true,
        data: {
          metrics: paginatedMetrics,
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total
          }
        }
      };
      
    } catch (error) {
      this.logger.error('获取性能指标失败:', error);
      throw new HttpException(
        error.message || '获取性能指标失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取实时性能指标
   */
  @Get('metrics/realtime/:nodeId')
  @ApiOperation({ summary: '获取实时性能指标' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '成功获取实时性能指标' })
  async getRealtimeMetrics(@Param('nodeId') nodeId: string) {
    try {
      const metrics = this.performanceService.getPerformanceMetrics(nodeId);
      const latestMetrics = metrics.length > 0 ? metrics[metrics.length - 1] : null;
      
      if (!latestMetrics) {
        throw new HttpException('未找到性能数据', HttpStatus.NOT_FOUND);
      }
      
      return {
        success: true,
        data: latestMetrics
      };
      
    } catch (error) {
      this.logger.error('获取实时性能指标失败:', error);
      throw new HttpException(
        error.message || '获取实时性能指标失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取性能统计摘要
   */
  @Get('metrics/summary/:nodeId')
  @ApiOperation({ summary: '获取性能统计摘要' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiQuery({ name: 'period', required: false, description: '统计周期(1h,6h,24h,7d)' })
  @ApiResponse({ status: 200, description: '成功获取性能统计摘要' })
  async getPerformanceSummary(
    @Param('nodeId') nodeId: string,
    @Query('period') period: string = '1h'
  ) {
    try {
      const metrics = this.performanceService.getPerformanceMetrics(nodeId);
      
      if (metrics.length === 0) {
        throw new HttpException('未找到性能数据', HttpStatus.NOT_FOUND);
      }
      
      // 计算时间范围
      const now = Date.now();
      let timeRange: number;
      
      switch (period) {
        case '1h':
          timeRange = 60 * 60 * 1000;
          break;
        case '6h':
          timeRange = 6 * 60 * 60 * 1000;
          break;
        case '24h':
          timeRange = 24 * 60 * 60 * 1000;
          break;
        case '7d':
          timeRange = 7 * 24 * 60 * 60 * 1000;
          break;
        default:
          timeRange = 60 * 60 * 1000;
      }
      
      const startTime = now - timeRange;
      const filteredMetrics = metrics.filter(m => m.timestamp >= startTime);
      
      if (filteredMetrics.length === 0) {
        throw new HttpException('指定时间范围内无数据', HttpStatus.NOT_FOUND);
      }
      
      // 计算统计数据
      const summary = this.calculatePerformanceSummary(filteredMetrics);
      
      return {
        success: true,
        data: {
          period,
          timeRange: { startTime, endTime: now },
          summary,
          dataPoints: filteredMetrics.length
        }
      };
      
    } catch (error) {
      this.logger.error('获取性能统计摘要失败:', error);
      throw new HttpException(
        error.message || '获取性能统计摘要失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取优化建议
   */
  @Get('recommendations/:nodeId')
  @ApiOperation({ summary: '获取优化建议' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '成功获取优化建议' })
  async getOptimizationRecommendations(@Param('nodeId') nodeId: string) {
    try {
      const recommendations = await this.performanceService.generateOptimizationRecommendations(nodeId);
      
      return {
        success: true,
        data: {
          nodeId,
          recommendations,
          generatedAt: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('获取优化建议失败:', error);
      throw new HttpException(
        error.message || '获取优化建议失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取优化配置
   */
  @Get('config/:nodeId')
  @ApiOperation({ summary: '获取优化配置' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '成功获取优化配置' })
  async getOptimizationConfig(@Param('nodeId') nodeId: string) {
    try {
      const config = this.performanceService.getOptimizationConfig(nodeId);
      
      if (!config) {
        throw new HttpException('未找到优化配置', HttpStatus.NOT_FOUND);
      }
      
      return {
        success: true,
        data: config
      };
      
    } catch (error) {
      this.logger.error('获取优化配置失败:', error);
      throw new HttpException(
        error.message || '获取优化配置失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新优化配置
   */
  @Put('config/:nodeId')
  @ApiOperation({ summary: '更新优化配置' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '成功更新优化配置' })
  async updateOptimizationConfig(
    @Param('nodeId') nodeId: string,
    @Body() configDto: OptimizationConfigDto
  ) {
    try {
      await this.performanceService.applyOptimizationConfig(nodeId, configDto);
      
      return {
        success: true,
        message: '优化配置已更新',
        data: {
          nodeId,
          config: configDto,
          updatedAt: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('更新优化配置失败:', error);
      throw new HttpException(
        error.message || '更新优化配置失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 触发手动优化
   */
  @Post('optimize/:nodeId')
  @ApiOperation({ summary: '触发手动优化' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  @ApiResponse({ status: 200, description: '成功触发优化' })
  async triggerOptimization(@Param('nodeId') nodeId: string) {
    try {
      // 获取当前配置并强制执行优化
      const config = this.performanceService.getOptimizationConfig(nodeId);
      
      if (!config) {
        throw new HttpException('未找到节点配置', HttpStatus.NOT_FOUND);
      }
      
      // 临时启用自动调优来触发优化
      await this.performanceService.applyOptimizationConfig(nodeId, {
        ...config,
        enableAutoTuning: true
      });
      
      return {
        success: true,
        message: '优化已触发',
        data: {
          nodeId,
          triggeredAt: Date.now()
        }
      };
      
    } catch (error) {
      this.logger.error('触发优化失败:', error);
      throw new HttpException(
        error.message || '触发优化失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 记录请求性能
   */
  @Post('record')
  @ApiOperation({ summary: '记录请求性能' })
  @ApiResponse({ status: 200, description: '成功记录性能数据' })
  async recordRequestPerformance(@Body() data: {
    responseTime: number;
    isError?: boolean;
  }) {
    try {
      this.performanceService.recordRequest(data.responseTime, data.isError);
      
      return {
        success: true,
        message: '性能数据已记录'
      };
      
    } catch (error) {
      this.logger.error('记录性能数据失败:', error);
      throw new HttpException(
        error.message || '记录性能数据失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取系统健康状态
   */
  @Get('health')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({ status: 200, description: '成功获取健康状态' })
  async getSystemHealth() {
    try {
      // 这里可以添加更复杂的健康检查逻辑
      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: Date.now(),
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage()
        }
      };
      
    } catch (error) {
      this.logger.error('获取健康状态失败:', error);
      throw new HttpException(
        error.message || '获取健康状态失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 计算性能统计摘要
   */
  private calculatePerformanceSummary(metrics: PerformanceMetrics[]) {
    if (metrics.length === 0) {
      return null;
    }
    
    const cpuUsages = metrics.map(m => m.cpu.usage);
    const memoryUsages = metrics.map(m => m.memory.usage);
    const responseTimes = metrics.map(m => m.application.averageResponseTime);
    const cacheHitRates = metrics.map(m => m.optimization.cacheHitRate);
    
    return {
      cpu: {
        average: this.calculateAverage(cpuUsages),
        min: Math.min(...cpuUsages),
        max: Math.max(...cpuUsages),
        current: cpuUsages[cpuUsages.length - 1]
      },
      memory: {
        average: this.calculateAverage(memoryUsages),
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages),
        current: memoryUsages[memoryUsages.length - 1]
      },
      responseTime: {
        average: this.calculateAverage(responseTimes),
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes),
        current: responseTimes[responseTimes.length - 1]
      },
      cacheHitRate: {
        average: this.calculateAverage(cacheHitRates),
        min: Math.min(...cacheHitRates),
        max: Math.max(...cacheHitRates),
        current: cacheHitRates[cacheHitRates.length - 1]
      },
      totalRequests: metrics.reduce((sum, m) => sum + m.application.requestsPerSecond, 0),
      errorRate: this.calculateAverage(metrics.map(m => m.application.errorRate))
    };
  }

  /**
   * 计算平均值
   */
  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }
}
